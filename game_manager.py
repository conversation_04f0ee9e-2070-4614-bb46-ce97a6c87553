import random
import string
import time
from typing import Dict, Optional, List
from dataclasses import dataclass, field
from enum import Enum

class GameState(Enum):
    WAITING = "waiting"
    PLAYING = "playing"
    FINISHED = "finished"

class Choice(Enum):
    ROCK = "rock"
    PAPER = "paper"
    SCISSORS = "scissors"

@dataclass
class Player:
    id: str
    name: str
    choice: Optional[Choice] = None
    connected: bool = True
    last_activity: float = field(default_factory=time.time)

@dataclass
class GameRoom:
    code: str
    players: List[Player] = field(default_factory=list)
    state: GameState = GameState.WAITING
    winner: Optional[str] = None
    created_at: float = field(default_factory=time.time)
    last_activity: float = field(default_factory=time.time)
    current_round: int = 1
    max_rounds: int = 10
    player_scores: Dict[str, int] = field(default_factory=dict)
    game_winner: Optional[str] = None
    
    def add_player(self, player: Player) -> bool:
        if len(self.players) >= 2:
            return False
        self.players.append(player)
        self.player_scores[player.id] = 0  # Initialize score
        self.last_activity = time.time()
        return True
    
    def remove_player(self, player_id: str) -> bool:
        for i, player in enumerate(self.players):
            if player.id == player_id:
                self.players.pop(i)
                self.last_activity = time.time()
                return True
        return False
    
    def get_player(self, player_id: str) -> Optional[Player]:
        for player in self.players:
            if player.id == player_id:
                return player
        return None
    
    def is_full(self) -> bool:
        return len(self.players) >= 2
    
    def both_players_chose(self) -> bool:
        return len(self.players) == 2 and all(p.choice is not None for p in self.players)
    
    def reset_choices(self):
        for player in self.players:
            player.choice = None
        self.winner = None
        self.state = GameState.PLAYING
        self.last_activity = time.time()

    def add_round_win(self, player_id: str):
        """Add a round win to a player's score"""
        if player_id in self.player_scores:
            self.player_scores[player_id] += 1

    def get_scores(self) -> Dict[str, int]:
        """Get current scores for all players"""
        return self.player_scores.copy()

    def is_game_complete(self) -> bool:
        """Check if the game is complete (early victory or max rounds reached)"""
        if not self.player_scores:
            return False

        scores = list(self.player_scores.values())
        max_score = max(scores)
        min_score = min(scores)

        # Early victory: First to reach majority of max rounds (6 out of 10)
        majority_needed = (self.max_rounds // 2) + 1
        if max_score >= majority_needed:
            return True

        # Check if it's mathematically impossible for the losing player to catch up
        rounds_remaining = self.max_rounds - self.current_round + 1
        if max_score - min_score > rounds_remaining:
            return True

        # Game complete after max rounds (unless there's a tie)
        if self.current_round > self.max_rounds:
            # If tied, continue playing until there's a winner
            if len(set(scores)) == 1:  # All scores are the same (tie)
                return False  # Continue playing
            return True

        return False

    def determine_game_winner(self) -> Optional[str]:
        """Determine the overall game winner"""
        if not self.player_scores:
            return None

        max_score = max(self.player_scores.values())
        winners = [pid for pid, score in self.player_scores.items() if score == max_score]

        # If there's only one player with the highest score, they win
        if len(winners) == 1:
            self.game_winner = winners[0]
            return winners[0]

        # If there are multiple winners (tie), only return None if we haven't exceeded max rounds
        # If we've exceeded max rounds and still tied, we continue playing
        return None  # Tie, need more rounds

    def advance_round(self):
        """Advance to the next round"""
        self.current_round += 1
        self.reset_choices()

class GameManager:
    def __init__(self):
        self.rooms: Dict[str, GameRoom] = {}
        self.player_to_room: Dict[str, str] = {}
    
    def generate_room_code(self) -> str:
        """Generate a unique 6-digit alphanumeric room code"""
        while True:
            code = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
            if code not in self.rooms:
                return code
    
    def create_room(self, player_id: str, player_name: str) -> str:
        """Create a new game room and add the creator as first player"""
        code = self.generate_room_code()
        room = GameRoom(code=code)
        player = Player(id=player_id, name=player_name)
        room.add_player(player)
        
        self.rooms[code] = room
        self.player_to_room[player_id] = code
        return code
    
    def join_room(self, code: str, player_id: str, player_name: str) -> tuple[bool, str]:
        """Join an existing room"""
        if code not in self.rooms:
            return False, "Room not found"
        
        room = self.rooms[code]
        if room.is_full():
            return False, "Room is full"
        
        player = Player(id=player_id, name=player_name)
        if room.add_player(player):
            self.player_to_room[player_id] = code
            if room.is_full():
                room.state = GameState.PLAYING
            return True, "Joined successfully"
        
        return False, "Failed to join room"
    
    def leave_room(self, player_id: str) -> Optional[str]:
        """Remove player from their current room"""
        if player_id not in self.player_to_room:
            return None
        
        code = self.player_to_room[player_id]
        room = self.rooms[code]
        room.remove_player(player_id)
        del self.player_to_room[player_id]
        
        # If room is empty, delete it
        if len(room.players) == 0:
            del self.rooms[code]
        else:
            # Reset room state if only one player left
            room.state = GameState.WAITING
            room.reset_choices()
        
        return code
    
    def make_choice(self, player_id: str, choice: str) -> tuple[bool, Optional[dict]]:
        """Player makes a choice (rock, paper, scissors)"""
        if player_id not in self.player_to_room:
            return False, None
        
        code = self.player_to_room[player_id]
        room = self.rooms[code]
        player = room.get_player(player_id)
        
        if not player or room.state != GameState.PLAYING:
            return False, None
        
        try:
            player.choice = Choice(choice.lower())
            player.last_activity = time.time()
            room.last_activity = time.time()
        except ValueError:
            return False, None
        
        # Check if both players have made their choice
        if room.both_players_chose():
            result = self._determine_winner(room)
            room.state = GameState.FINISHED
            return True, result
        
        return True, None
    
    def _determine_winner(self, room: GameRoom) -> dict:
        """Determine the winner of the round and update game state"""
        p1, p2 = room.players[0], room.players[1]

        # Store choices before they get reset
        p1_choice = p1.choice
        p2_choice = p2.choice
        current_round_number = room.current_round

        # Determine round winner
        if p1_choice == p2_choice:
            round_result = "tie"
            round_winner_id = None
        elif (
            (p1_choice == Choice.ROCK and p2_choice == Choice.SCISSORS) or
            (p1_choice == Choice.PAPER and p2_choice == Choice.ROCK) or
            (p1_choice == Choice.SCISSORS and p2_choice == Choice.PAPER)
        ):
            round_result = "win"
            round_winner_id = p1.id
            room.winner = p1.id
            room.add_round_win(p1.id)
        else:
            round_result = "win"
            round_winner_id = p2.id
            room.winner = p2.id
            room.add_round_win(p2.id)

        # Check if game is complete
        game_complete = room.is_game_complete()
        game_winner_id = None

        if game_complete:
            game_winner_id = room.determine_game_winner()
        else:
            # Advance to next round
            room.advance_round()

        return {
            "round_result": round_result,
            "round_winner_id": round_winner_id,
            "game_complete": game_complete,
            "game_winner_id": game_winner_id,
            "current_round": current_round_number,  # Show the round that just finished
            "total_rounds": room.max_rounds,
            "scores": room.get_scores(),
            "choices": {
                p1.id: p1_choice.value,
                p2.id: p2_choice.value
            },
            "players": {
                p1.id: p1.name,
                p2.id: p2.name
            }
        }
    
    def get_room(self, code: str) -> Optional[GameRoom]:
        """Get room by code"""
        return self.rooms.get(code)
    
    def get_player_room(self, player_id: str) -> Optional[GameRoom]:
        """Get room that player is in"""
        if player_id not in self.player_to_room:
            return None
        code = self.player_to_room[player_id]
        return self.rooms.get(code)
    
    def cleanup_expired_rooms(self, max_age_minutes: int = 30):
        """Remove rooms that have been inactive for too long"""
        current_time = time.time()
        expired_codes = []
        
        for code, room in self.rooms.items():
            if current_time - room.last_activity > max_age_minutes * 60:
                expired_codes.append(code)
        
        for code in expired_codes:
            room = self.rooms[code]
            # Remove player mappings
            for player in room.players:
                if player.id in self.player_to_room:
                    del self.player_to_room[player.id]
            # Remove room
            del self.rooms[code]
        
        return len(expired_codes)
