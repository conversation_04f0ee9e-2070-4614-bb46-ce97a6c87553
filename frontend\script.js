// Game State Management
class GameClient {
    constructor() {
        this.socket = null;
        this.playerId = null;
        this.playerName = '';
        this.roomCode = '';
        this.currentScreen = 'welcome-screen';
        this.gameState = 'waiting';
        this.players = [];
        
        this.init();
    }
    
    init() {
        this.connectSocket();
        this.setupEventListeners();
        this.showScreen('welcome-screen');
    }

    getDefaultServerUrl() {
        // Fallback configuration if config.js is not loaded
        const isLocal = window.location.hostname === 'localhost' ||
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname === '';

        return isLocal
            ? 'http://localhost:5000'
            : 'https://your-app-name.onrender.com'; // Replace with your actual URL
    }

    connectSocket() {
        // Get server URL from configuration
        const serverUrl = typeof CONFIG !== 'undefined'
            ? CONFIG.getServerUrl()
            : this.getDefaultServerUrl();

        this.socket = io(serverUrl, {
            transports: ['websocket', 'polling'],
            timeout: 20000,
            forceNew: true
        });
        
        this.socket.on('connect', () => {
            this.updateConnectionStatus(true);
            this.showToast('Connected to server!');
            console.log('Connected to server successfully');
        });
        
        this.socket.on('disconnect', () => {
            this.updateConnectionStatus(false);
            this.showToast('Disconnected from server', 'error');
            console.log('Disconnected from server');
        });
        
        this.socket.on('connect_error', (error) => {
            this.updateConnectionStatus(false);
            this.showToast('Connection failed. Please try again.', 'error');
            console.error('Connection error:', error);
        });
        
        this.socket.on('connected', (data) => {
            this.playerId = data.player_id;
            console.log('Player ID assigned:', this.playerId);
        });
        
        this.socket.on('room_created', (data) => {
            this.roomCode = data.room_code;
            this.playerName = data.player_name;
            this.showScreen('waiting-screen');
            this.updateRoomDisplay();
            this.showToast(`Room ${this.roomCode} created!`);
        });
        
        this.socket.on('room_joined', (data) => {
            this.roomCode = data.room_code;
            this.playerName = data.player_name;
            this.players = data.players;
            this.showScreen('waiting-screen');
            this.updateRoomDisplay();
            this.updatePlayersList();
            this.showToast(`Joined room ${this.roomCode}!`);
        });
        
        this.socket.on('join_error', (data) => {
            this.showError('join-error', data.message);
        });
        
        this.socket.on('player_joined', (data) => {
            this.players = data.players;
            this.updatePlayersList();
            
            if (data.game_ready) {
                this.showScreen('game-screen');
                this.updateGameHeader();
                // Initialize round info for new game
                const initialScores = {};
                data.players.forEach(player => {
                    initialScores[player.id] = 0;
                });
                this.updateRoundInfo(1, initialScores);
                this.showToast('Game is starting!');
            } else {
                this.showToast(`${data.player_name} joined the room!`);
            }
        });
        
        this.socket.on('player_left', (data) => {
            this.showToast(data.message, 'error');
            this.showScreen('waiting-screen');
            this.updateWaitingMessage();
        });
        
        this.socket.on('choice_recorded', (data) => {
            this.showChoiceFeedback('Choice recorded! Waiting for opponent...', 'recorded');
            this.disableChoiceButtons();
        });
        
        this.socket.on('player_chose', (data) => {
            if (data.player_id !== this.playerId) {
                this.showChoiceFeedback('Opponent has chosen! Waiting for you...', 'waiting');
            }
        });
        
        this.socket.on('game_result', (data) => {
            this.showResults(data);
        });
        
        this.socket.on('new_round', (data) => {
            this.showScreen('game-screen');
            this.resetGameState();
            this.updateRoundInfo(data.current_round, data.scores);
            this.showToast(data.message);
        });
    }
    
    setupEventListeners() {
        // Welcome screen
        document.getElementById('create-room-btn').addEventListener('click', () => {
            this.createRoom();
        });
        
        document.getElementById('join-room-btn').addEventListener('click', () => {
            this.showScreen('join-screen');
        });
        
        // Join screen
        document.getElementById('join-confirm-btn').addEventListener('click', () => {
            this.joinRoom();
        });
        
        document.getElementById('back-to-welcome-btn').addEventListener('click', () => {
            this.showScreen('welcome-screen');
        });
        
        // Room code input formatting
        document.getElementById('room-code').addEventListener('input', (e) => {
            e.target.value = e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '');
        });
        
        // Enter key handling
        document.getElementById('player-name').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('create-room-btn').click();
            }
        });
        
        document.getElementById('room-code').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                document.getElementById('join-confirm-btn').click();
            }
        });
        
        // Waiting screen
        document.getElementById('copy-code-btn').addEventListener('click', () => {
            this.copyRoomCode();
        });
        
        document.getElementById('leave-room-btn').addEventListener('click', () => {
            this.leaveRoom();
        });
        
        // Game screen
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.makeChoice(e.currentTarget.dataset.choice);
            });
        });
        
        // Results screen
        document.getElementById('play-again-btn').addEventListener('click', () => {
            this.playAgain();
        });
        
        document.getElementById('leave-game-btn').addEventListener('click', () => {
            this.leaveRoom();
        });
    }
    
    createRoom() {
        const playerName = document.getElementById('player-name').value.trim();
        if (!playerName) {
            this.showToast('Please enter your name', 'error');
            return;
        }
        
        if (!this.socket || !this.socket.connected) {
            this.showToast('Not connected to server. Please wait...', 'error');
            return;
        }
        
        this.socket.emit('create_room', { player_name: playerName });
    }
    
    joinRoom() {
        const playerName = document.getElementById('player-name').value.trim();
        const roomCode = document.getElementById('room-code').value.trim();
        
        if (!playerName) {
            this.showToast('Please enter your name', 'error');
            return;
        }
        
        if (!roomCode || roomCode.length !== 6) {
            this.showError('join-error', 'Please enter a valid 6-digit room code');
            return;
        }
        
        if (!this.socket || !this.socket.connected) {
            this.showToast('Not connected to server. Please wait...', 'error');
            return;
        }
        
        this.hideError('join-error');
        this.socket.emit('join_room', { 
            player_name: playerName, 
            room_code: roomCode 
        });
    }
    
    makeChoice(choice) {
        if (!this.socket || !this.socket.connected) {
            this.showToast('Not connected to server', 'error');
            return;
        }
        
        this.socket.emit('make_choice', { choice: choice });
        
        // Visual feedback
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.classList.remove('selected');
        });
        document.querySelector(`[data-choice="${choice}"]`).classList.add('selected');
    }
    
    playAgain() {
        if (!this.socket || !this.socket.connected) {
            this.showToast('Not connected to server', 'error');
            return;
        }
        
        this.socket.emit('play_again', {});
    }
    
    leaveRoom() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket.connect();
        }
        this.showScreen('welcome-screen');
        this.resetGameState();
        this.showToast('Left the room');
    }
    
    showScreen(screenId) {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.remove('active');
        });
        document.getElementById(screenId).classList.add('active');
        this.currentScreen = screenId;
    }
    
    updateConnectionStatus(connected) {
        const indicator = document.getElementById('connection-indicator');
        const text = document.getElementById('connection-text');
        
        if (connected) {
            indicator.textContent = '🟢';
            text.textContent = 'Connected';
        } else {
            indicator.textContent = '🔴';
            text.textContent = 'Disconnected';
        }
    }
    
    updateRoomDisplay() {
        document.getElementById('display-room-code').textContent = this.roomCode;
        document.getElementById('game-room-code').textContent = this.roomCode;
    }
    
    updatePlayersList() {
        const container = document.getElementById('players-container');
        container.innerHTML = '';
        
        this.players.forEach(player => {
            const playerDiv = document.createElement('div');
            playerDiv.className = 'player-item';
            playerDiv.innerHTML = `
                <span>${player.name} ${player.id === this.playerId ? '(You)' : ''}</span>
                <span class="player-status">${player.connected ? '🟢 Online' : '🔴 Offline'}</span>
            `;
            container.appendChild(playerDiv);
        });
        
        this.updateWaitingMessage();
    }
    
    updateWaitingMessage() {
        const message = document.getElementById('waiting-message');
        if (this.players.length < 2) {
            message.textContent = 'Waiting for another player to join...';
            message.classList.add('pulse');
        } else {
            message.textContent = 'Game is ready to start!';
            message.classList.remove('pulse');
        }
    }
    
    updateGameHeader(scores = null) {
        const playersInfo = document.getElementById('game-players');
        if (scores && this.players.length === 2) {
            // Show players with scores
            const player1 = this.players[0];
            const player2 = this.players[1];
            const score1 = scores[player1.id] || 0;
            const score2 = scores[player2.id] || 0;

            playersInfo.innerHTML = `
                <span>${player1.name}${player1.id === this.playerId ? ' (You)' : ''}: ${score1}</span>
                <span class="vs-text"> vs </span>
                <span>${player2.name}${player2.id === this.playerId ? ' (You)' : ''}: ${score2}</span>
            `;
        } else {
            // Show players without scores (initial state)
            playersInfo.innerHTML = this.players.map(p =>
                `<span>${p.name}${p.id === this.playerId ? ' (You)' : ''}</span>`
            ).join(' vs ');
        }
    }
    
    showChoiceFeedback(message, type) {
        const feedback = document.getElementById('choice-feedback');
        feedback.textContent = message;
        feedback.className = `choice-feedback show ${type}`;
    }
    
    disableChoiceButtons() {
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.disabled = true;
        });
    }
    
    resetGameState() {
        document.querySelectorAll('.choice-btn').forEach(btn => {
            btn.disabled = false;
            btn.classList.remove('selected');
        });

        document.getElementById('choice-feedback').classList.remove('show');
        // Don't reset game-status here as it will be updated by updateRoundInfo
    }
    
    showResults(data) {
        this.showScreen('results-screen');

        const choiceEmojis = {
            rock: '✊',
            paper: '✋',
            scissors: '✌️'
        };
        
        // Update player choices display
        const players = Object.keys(data.players);
        const player1Id = players[0];
        const player2Id = players[1];
        
        document.getElementById('player1-name').textContent = data.players[player1Id];
        document.getElementById('player2-name').textContent = data.players[player2Id];
        document.getElementById('player1-choice').textContent = choiceEmojis[data.choices[player1Id]];
        document.getElementById('player2-choice').textContent = choiceEmojis[data.choices[player2Id]];
        
        // Update result message
        const resultMessage = document.getElementById('result-message');
        const resultTitle = document.getElementById('result-title');

        // Update round info and scores
        this.updateRoundInfo(data.current_round, data.scores);

        if (data.game_complete) {
            // Show final game result
            if (data.game_winner_id === this.playerId) {
                resultTitle.textContent = "🏆 You Win the Game! 🏆";
                resultMessage.textContent = `Congratulations! You won ${data.scores[this.playerId]} out of ${data.current_round} rounds!`;
                resultMessage.className = 'result-message win';
            } else if (data.game_winner_id && data.game_winner_id !== this.playerId) {
                resultTitle.textContent = "Game Over 😔";
                resultMessage.textContent = `You lost the game. Final score: ${data.scores[this.playerId]} - ${data.scores[data.game_winner_id]}`;
                resultMessage.className = 'result-message lose';
            } else {
                // Handle case where game_winner_id is null/undefined (shouldn't happen with new logic)
                resultTitle.textContent = "Game Complete";
                resultMessage.textContent = `Game ended. Final score: ${data.scores[this.playerId]} - ${Object.values(data.scores).find(score => score !== data.scores[this.playerId]) || 0}`;
                resultMessage.className = 'result-message tie';
            }
        } else {
            // Show round result
            if (data.round_result === 'tie') {
                resultTitle.textContent = `Round ${data.current_round} - Tie!`;
                resultMessage.textContent = "Great minds think alike! No points awarded.";
                resultMessage.className = 'result-message tie';
            } else if (data.round_winner_id === this.playerId) {
                resultTitle.textContent = `Round ${data.current_round} - You Win! 🎉`;
                resultMessage.textContent = "You earned a point this round!";
                resultMessage.className = 'result-message win';
            } else {
                resultTitle.textContent = `Round ${data.current_round} - You Lose 😔`;
                resultMessage.textContent = "Your opponent earned a point this round.";
                resultMessage.className = 'result-message lose';
            }
        }
    }

    updateRoundInfo(currentRound, scores) {
        // Update round info in game screen
        const gameStatus = document.getElementById('game-status');
        if (gameStatus && scores) {
            const playerIds = Object.keys(scores);
            const myScore = scores[this.playerId] || 0;
            const opponentScore = playerIds.length > 1 ? scores[playerIds.find(id => id !== this.playerId)] || 0 : 0;

            // Update game status to show round and score
            gameStatus.innerHTML = `
                <div>Round ${currentRound} of 10</div>
                <div>Score: ${myScore} - ${opponentScore}</div>
                <div>Make your choice!</div>
            `;

            // Also update the game header with scores
            this.updateGameHeader(scores);
        }
    }

    copyRoomCode() {
        navigator.clipboard.writeText(this.roomCode).then(() => {
            this.showToast('Room code copied to clipboard!');
        }).catch(() => {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = this.roomCode;
            document.body.appendChild(textArea);
            textArea.select();
            try {
                document.execCommand('copy');
                this.showToast('Room code copied to clipboard!');
            } catch (err) {
                this.showToast('Failed to copy room code', 'error');
            }
            document.body.removeChild(textArea);
        });
    }
    
    showToast(message, type = 'info') {
        const container = document.getElementById('toast-container');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;
        
        container.appendChild(toast);
        
        setTimeout(() => {
            toast.remove();
        }, 3000);
    }
    
    showError(elementId, message) {
        const errorElement = document.getElementById(elementId);
        errorElement.textContent = message;
        errorElement.classList.add('show');
    }
    
    hideError(elementId) {
        const errorElement = document.getElementById(elementId);
        errorElement.classList.remove('show');
    }
}

// Initialize the game when the page loads
document.addEventListener('DOMContentLoaded', () => {
    new GameClient();
});
