<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rock Paper Scissors Online</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.7.2/socket.io.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>🎮 Rock Paper Scissors Online</h1>
            <p>Challenge your friends in real-time!</p>
        </header>

        <!-- Welcome Screen -->
        <div id="welcome-screen" class="screen active">
            <div class="welcome-content">
                <div class="input-group">
                    <label for="player-name">Enter your name:</label>
                    <input type="text" id="player-name" placeholder="Your name" maxlength="20">
                </div>
                
                <div class="button-group">
                    <button id="create-room-btn" class="btn btn-primary">
                        <span class="btn-icon">🎯</span>
                        Create New Game
                    </button>
                    <button id="join-room-btn" class="btn btn-secondary">
                        <span class="btn-icon">🚪</span>
                        Join Game
                    </button>
                </div>
            </div>
        </div>

        <!-- Join Room Screen -->
        <div id="join-screen" class="screen">
            <div class="join-content">
                <h2>Join a Game</h2>
                <div class="input-group">
                    <label for="room-code">Enter 6-digit room code:</label>
                    <input type="text" id="room-code" placeholder="ABC123" maxlength="6" style="text-transform: uppercase;">
                </div>
                <div class="button-group">
                    <button id="join-confirm-btn" class="btn btn-primary">Join Game</button>
                    <button id="back-to-welcome-btn" class="btn btn-secondary">Back</button>
                </div>
                <div id="join-error" class="error-message"></div>
            </div>
        </div>

        <!-- Waiting Room Screen -->
        <div id="waiting-screen" class="screen">
            <div class="waiting-content">
                <h2>Game Room</h2>
                <div class="room-info">
                    <div class="room-code-display">
                        <label>Room Code:</label>
                        <div class="room-code" id="display-room-code">------</div>
                        <button id="copy-code-btn" class="btn btn-small">📋 Copy</button>
                    </div>
                </div>
                
                <div class="players-list">
                    <h3>Players</h3>
                    <div id="players-container">
                        <!-- Players will be dynamically added here -->
                    </div>
                </div>
                
                <div id="waiting-message" class="status-message">
                    Waiting for another player to join...
                </div>
                
                <button id="leave-room-btn" class="btn btn-danger">Leave Room</button>
            </div>
        </div>

        <!-- Game Screen -->
        <div id="game-screen" class="screen">
            <div class="game-content">
                <div class="game-header">
                    <div class="room-code-small">Room: <span id="game-room-code">------</span></div>
                    <div class="players-info" id="game-players">
                        <!-- Player info will be shown here -->
                    </div>
                </div>
                
                <div class="game-status" id="game-status">
                    <div>Round 1 of 10</div>
                    <div>Score: 0 - 0</div>
                    <div>Make your choice!</div>
                </div>
                
                <div class="choices-container">
                    <button class="choice-btn" data-choice="rock">
                        <span class="choice-emoji">✊</span>
                        <span class="choice-name">Rock</span>
                    </button>
                    <button class="choice-btn" data-choice="paper">
                        <span class="choice-emoji">✋</span>
                        <span class="choice-name">Paper</span>
                    </button>
                    <button class="choice-btn" data-choice="scissors">
                        <span class="choice-emoji">✌️</span>
                        <span class="choice-name">Scissors</span>
                    </button>
                </div>
                
                <div id="choice-feedback" class="choice-feedback"></div>
            </div>
        </div>

        <!-- Results Screen -->
        <div id="results-screen" class="screen">
            <div class="results-content">
                <div class="result-header">
                    <h2 id="result-title">Game Result</h2>
                </div>
                
                <div class="choices-display">
                    <div class="player-choice">
                        <div class="player-name" id="player1-name">Player 1</div>
                        <div class="choice-display" id="player1-choice">✊</div>
                    </div>

                    <div class="vs-divider">VS</div>

                    <div class="player-choice">
                        <div class="player-name" id="player2-name">Player 2</div>
                        <div class="choice-display" id="player2-choice">✋</div>
                    </div>
                </div>
                
                <div class="result-message" id="result-message">
                    <!-- Result message will be shown here -->
                </div>
                
                <div class="button-group">
                    <button id="play-again-btn" class="btn btn-primary">
                        <span class="btn-icon">🔄</span>
                        Play Again
                    </button>
                    <button id="leave-game-btn" class="btn btn-secondary">
                        <span class="btn-icon">🚪</span>
                        Leave Game
                    </button>
                </div>
            </div>
        </div>

        <!-- Connection Status -->
        <div id="connection-status" class="connection-status">
            <span id="connection-indicator">🔴</span>
            <span id="connection-text">Connecting...</span>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toast-container" class="toast-container"></div>

    <script src="config.js"></script>
    <script src="script.js"></script>
</body>
</html>
