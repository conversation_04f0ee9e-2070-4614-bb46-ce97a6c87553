#!/usr/bin/env python3
"""
Test script to verify the new ending logic:
- Game ends at 10 rounds if 2+ point lead
- Game continues if 1 point difference until someone wins 2 in a row
"""

from game_manager import <PERSON><PERSON><PERSON>, Player

def test_clear_victory_at_10_rounds():
    """Test that game ends at round 10 with 2+ point lead"""
    room = GameRoom(code="TEST1")
    player1 = Player(id="p1", name="Player 1")
    player2 = Player(id="p2", name="Player 2")
    
    room.add_player(player1)
    room.add_player(player2)
    
    # Simulate 10 rounds: P1 wins 7, P2 wins 3 (7-3 = 4 point lead)
    for i in range(10):
        if i < 7:
            room.add_round_win("p1")
            room.add_round_result("p1")
        else:
            room.add_round_win("p2")
            room.add_round_result("p2")
        room.current_round = i + 1
    
    print(f"Test 1 - Clear victory (7-3 after 10 rounds):")
    print(f"Scores: {room.get_scores()}")
    print(f"Current round: {room.current_round}")
    print(f"Game complete: {room.is_game_complete()}")
    print(f"Winner: {room.determine_game_winner()}")
    print(f"Expected: True (game should end)\n")

def test_close_game_continues():
    """Test that game continues with 1 point difference after 10 rounds"""
    room = GameRoom(code="TEST2")
    player1 = Player(id="p1", name="Player 1")
    player2 = Player(id="p2", name="Player 2")
    
    room.add_player(player1)
    room.add_player(player2)
    
    # Simulate 10 rounds: P1 wins 6, P2 wins 4 (6-4 = 2 point lead)
    for i in range(10):
        if i < 6:
            room.add_round_win("p1")
            room.add_round_result("p1")
        else:
            room.add_round_win("p2")
            room.add_round_result("p2")
        room.current_round = i + 1
    
    print(f"Test 2 - 2-point lead (6-4 after 10 rounds):")
    print(f"Scores: {room.get_scores()}")
    print(f"Current round: {room.current_round}")
    print(f"Game complete: {room.is_game_complete()}")
    print(f"Expected: True (2+ point lead, game should end)\n")

def test_one_point_difference():
    """Test that game continues with 1 point difference"""
    room = GameRoom(code="TEST3")
    player1 = Player(id="p1", name="Player 1")
    player2 = Player(id="p2", name="Player 2")
    
    room.add_player(player1)
    room.add_player(player2)
    
    # Simulate 10 rounds: P1 wins 6, P2 wins 4, but make it 5-5 first then 6-5
    for i in range(10):
        if i < 5:
            room.add_round_win("p1")
            room.add_round_result("p1")
        elif i < 10:
            room.add_round_win("p2")
            room.add_round_result("p2")
        room.current_round = i + 1
    
    # Now make it 6-5 (1 point difference)
    room.add_round_win("p1")
    room.add_round_result("p1")
    room.current_round = 11
    
    print(f"Test 3 - 1-point difference (6-5 after 11 rounds):")
    print(f"Scores: {room.get_scores()}")
    print(f"Current round: {room.current_round}")
    print(f"Game complete: {room.is_game_complete()}")
    print(f"Recent winners: {room.recent_winners}")
    print(f"Expected: False (1 point diff, no consecutive wins)\n")

def test_consecutive_wins_after_10():
    """Test that game ends when someone wins 2 in a row after round 10"""
    room = GameRoom(code="TEST4")
    player1 = Player(id="p1", name="Player 1")
    player2 = Player(id="p2", name="Player 2")
    
    room.add_player(player1)
    room.add_player(player2)
    
    # Simulate getting to 6-5 after round 11
    room.player_scores["p1"] = 6
    room.player_scores["p2"] = 5
    room.current_round = 11
    
    # Simulate the last few rounds to get consecutive wins
    room.add_round_result("p2")  # Round 10: P2 wins
    room.add_round_result("p1")  # Round 11: P1 wins (current state)
    
    # Now P1 wins round 12 (2 consecutive wins)
    room.add_round_win("p1")
    room.add_round_result("p1")
    room.current_round = 12
    
    print(f"Test 4 - Consecutive wins (P1 wins rounds 11 & 12):")
    print(f"Scores: {room.get_scores()}")
    print(f"Current round: {room.current_round}")
    print(f"Recent winners: {room.recent_winners}")
    print(f"Has consecutive wins: {room.has_consecutive_wins()}")
    print(f"Game complete: {room.is_game_complete()}")
    print(f"Expected: True (consecutive wins should end game)\n")

def test_tie_scenario():
    """Test tie scenario"""
    room = GameRoom(code="TEST5")
    player1 = Player(id="p1", name="Player 1")
    player2 = Player(id="p2", name="Player 2")
    
    room.add_player(player1)
    room.add_player(player2)
    
    # Simulate 10 rounds: 5-5 tie
    for i in range(10):
        if i < 5:
            room.add_round_win("p1")
            room.add_round_result("p1")
        else:
            room.add_round_win("p2")
            room.add_round_result("p2")
        room.current_round = i + 1
    
    print(f"Test 5 - Tie after 10 rounds (5-5):")
    print(f"Scores: {room.get_scores()}")
    print(f"Current round: {room.current_round}")
    print(f"Game complete: {room.is_game_complete()}")
    print(f"Expected: False (tie, should continue)\n")

if __name__ == "__main__":
    print("Testing new ending logic...\n")
    
    test_clear_victory_at_10_rounds()
    test_close_game_continues()
    test_one_point_difference()
    test_consecutive_wins_after_10()
    test_tie_scenario()
    
    print("All tests completed!")
