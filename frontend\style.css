/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

@import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&family=Orbitron:wght@400;700;900&display=swap');

body {
    font-family: 'Press Start 2P', 'Courier New', monospace;
    background:
        linear-gradient(45deg, #2a0845 0%, #4a1a5c 25%, #6b2c7a 50%, #8b3f99 75%, #ab52b8 100%),
        repeating-linear-gradient(
            90deg,
            transparent 0px,
            transparent 2px,
            rgba(255, 255, 255, 0.03) 2px,
            rgba(255, 255, 255, 0.03) 4px
        );
    min-height: 100vh;
    color: #00ffff;
    overflow-x: hidden;
    position: relative;
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

/* 90s Grid Pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    pointer-events: none;
    z-index: 1;
    animation: gridMove 20s linear infinite;
}

/* Moving grid animation */
@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(20px, 20px); }
}

/* 90s Starfield Effect */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(1px 1px at 25px 35px, #ffffff, transparent),
        radial-gradient(1px 1px at 85px 15px, #ffff00, transparent),
        radial-gradient(1px 1px at 155px 95px, #ff00ff, transparent),
        radial-gradient(1px 1px at 195px 45px, #00ff00, transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: starfield 15s linear infinite;
    pointer-events: none;
    z-index: 2;
    opacity: 0.7;
}

@keyframes starfield {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-200px); }
}

.container {
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    z-index: 2;
}

/* Header */
header {
    text-align: center;
    margin-bottom: 2rem;
    position: relative;
}

header h1 {
    font-family: 'Press Start 2P', monospace;
    font-size: 2.2rem;
    margin-bottom: 1rem;
    color: #ffff00;
    text-shadow:
        3px 3px 0px #ff00ff,
        6px 6px 0px #00ffff,
        9px 9px 0px #ff0080;
    position: relative;
    letter-spacing: 2px;
    animation: neon90s 2s ease-in-out infinite alternate;
    z-index: 10;
}

header h1::before {
    content: '★ ';
    color: #ff0080;
    animation: starSpin 3s linear infinite;
}

header h1::after {
    content: ' ★';
    color: #ff0080;
    animation: starSpin 3s linear infinite reverse;
}

@keyframes neon90s {
    0% {
        text-shadow:
            3px 3px 0px #ff00ff,
            6px 6px 0px #00ffff,
            9px 9px 0px #ff0080,
            0 0 20px #ffff00;
    }
    100% {
        text-shadow:
            3px 3px 0px #ff00ff,
            6px 6px 0px #00ffff,
            9px 9px 0px #ff0080,
            0 0 40px #ffff00,
            0 0 60px #ffff00;
    }
}

@keyframes starSpin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

header p {
    font-family: 'Press Start 2P', monospace;
    font-size: 0.8rem;
    color: #00ff00;
    text-shadow:
        2px 2px 0px #000000,
        0 0 10px #00ff00;
    margin-top: 1rem;
    animation: textBlink 1.5s ease-in-out infinite alternate;
    z-index: 10;
    position: relative;
}

@keyframes textBlink {
    0% {
        opacity: 0.8;
        text-shadow:
            2px 2px 0px #000000,
            0 0 10px #00ff00;
    }
    100% {
        opacity: 1;
        text-shadow:
            2px 2px 0px #000000,
            0 0 20px #00ff00;
    }
}

/* Screen Management */
.screen {
    display: none;
    flex: 1;
    background:
        repeating-linear-gradient(
            45deg,
            #000000 0px,
            #000000 2px,
            #111111 2px,
            #111111 4px
        ),
        linear-gradient(135deg, #1a0033 0%, #330066 100%);
    border: 4px solid #00ffff;
    border-radius: 0;
    padding: 2rem;
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 4px #00ffff,
        0 0 20px #00ffff,
        0 8px 16px rgba(0, 0, 0, 0.8);

    position: relative;
    overflow: hidden;
}

.screen::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        repeating-linear-gradient(
            0deg,
            transparent 0px,
            transparent 1px,
            rgba(0, 255, 0, 0.05) 1px,
            rgba(0, 255, 0, 0.05) 2px
        );
    pointer-events: none;
    animation: retroScan 0.1s linear infinite;
}

.screen.active {
    display: flex;
    flex-direction: column;
}



@keyframes retroScan {
    0% { transform: translateY(0px); }
    100% { transform: translateY(2px); }
}

/* Welcome Screen */
.welcome-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 2rem;
}

/* Join Screen */
.join-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 2rem;
}

/* Input Groups */
.input-group {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    width: 100%;
    max-width: 350px;
    position: relative;
}

.input-group label {
    font-family: 'Press Start 2P', monospace;
    font-size: 0.7rem;
    color: #ffff00;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow:
        2px 2px 0px #000000,
        0 0 10px #ffff00;
    margin-bottom: 0.5rem;
}

input[type="text"] {
    padding: 12px 16px;
    border: 3px solid #00ffff;
    border-radius: 0;
    font-size: 1rem;
    font-family: 'Press Start 2P', monospace;
    transition: all 0.2s ease;
    background: #000000;
    color: #00ffff;
    box-shadow:
        inset 0 0 0 1px #000000,
        inset 0 0 0 3px #00ffff,
        3px 3px 0px #000000,
        0 0 15px #00ffff;
    text-shadow: 0 0 5px #00ffff;
}

input[type="text"]:focus {
    outline: none;
    border-color: #ffff00;
    background: #000000;
    color: #ffff00;
    box-shadow:
        inset 0 0 0 1px #000000,
        inset 0 0 0 3px #ffff00,
        3px 3px 0px #000000,
        0 0 20px #ffff00;
    text-shadow: 0 0 8px #ffff00;
    animation: input90sBlink 0.5s ease-in-out;
}

@keyframes input90sBlink {
    0%, 100% { background: #000000; }
    50% { background: #001122; }
}

input[type="text"]::placeholder {
    color: #006666;
    text-shadow: none;
}

/* Buttons */
.button-group {
    display: flex;
    flex-direction: column;
    gap: 1.2rem;
    width: 100%;
    max-width: 350px;
}

.btn {
    padding: 14px 20px;
    border: 3px solid;
    border-radius: 0;
    font-family: 'Press Start 2P', monospace;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.1s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow:
        inset 0 0 0 1px #000000,
        4px 4px 0px #000000;
    text-shadow: 2px 2px 0px #000000;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.4);
}

.btn:active {
    transform: translateY(0) scale(0.98);
}

.btn-primary {
    background: #ff0080;
    border-color: #ff0080;
    color: #ffffff;
}

.btn-primary:hover {
    background: #ff00ff;
    border-color: #ff00ff;
}

.btn-secondary {
    background: #00ffff;
    border-color: #00ffff;
    color: #000000;
}

.btn-secondary:hover {
    background: #ffff00;
    border-color: #ffff00;
    color: #000000;
}

.btn-danger {
    background: #ff4000;
    border-color: #ff4000;
    color: #ffffff;
}

.btn-danger:hover {
    background: #ff0000;
    border-color: #ff0000;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.6rem;
    background: #00ff00;
    border-color: #00ff00;
    color: #000000;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* Room Code Display */
.room-info {
    text-align: center;
    margin-bottom: 2rem;
}

.room-code-display {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1.5rem;
    padding: 2rem;
    background: linear-gradient(135deg, rgba(0, 245, 255, 0.2) 0%, rgba(255, 0, 255, 0.2) 100%);
    border: 2px solid #00f5ff;
    border-radius: 20px;
    color: #e0e6ed;
    box-shadow:
        0 0 40px rgba(0, 245, 255, 0.4),
        inset 0 0 40px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
}

.room-code-display::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: roomCodeScan 4s ease-in-out infinite;
}

@keyframes roomCodeScan {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

.room-code-display label {
    font-family: 'Orbitron', monospace;
    font-size: 1.2rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: #64ffda;
    text-shadow: 0 0 15px rgba(100, 255, 218, 0.8);
}

.room-code {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 900;
    letter-spacing: 0.3em;
    padding: 1rem 1.5rem;
    background: rgba(0, 0, 0, 0.5);
    border: 2px solid #ffff00;
    border-radius: 15px;
    color: #ffff00;
    text-shadow: 0 0 20px rgba(255, 255, 0, 0.8);
    box-shadow:
        0 0 30px rgba(255, 255, 0, 0.4),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    animation: codeGlow 2s ease-in-out infinite alternate;
    position: relative;
    z-index: 1;
}

@keyframes codeGlow {
    from {
        text-shadow: 0 0 20px rgba(255, 255, 0, 0.8);
        box-shadow:
            0 0 30px rgba(255, 255, 0, 0.4),
            inset 0 0 20px rgba(255, 255, 255, 0.1);
    }
    to {
        text-shadow: 0 0 30px rgba(255, 255, 0, 1);
        box-shadow:
            0 0 50px rgba(255, 255, 0, 0.6),
            inset 0 0 20px rgba(255, 255, 255, 0.15);
    }
}

/* Players List */
.players-list h3 {
    text-align: center;
    margin-bottom: 1rem;
    color: #555;
}

.players-container {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.player-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 10px;
    font-weight: 500;
}

.player-status {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Game Screen */
.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.room-code-small {
    font-weight: 600;
    color: #667eea;
}

.game-status {
    text-align: center;
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(0, 245, 255, 0.2) 0%, rgba(255, 0, 255, 0.2) 100%);
    border: 2px solid #00f5ff;
    border-radius: 15px;
    color: #64ffda;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-shadow: 0 0 20px rgba(100, 255, 218, 0.8);
    box-shadow:
        0 0 30px rgba(0, 245, 255, 0.3),
        inset 0 0 30px rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    animation: statusGlow 2s ease-in-out infinite alternate;
}

@keyframes statusGlow {
    from {
        text-shadow: 0 0 20px rgba(100, 255, 218, 0.8);
        box-shadow:
            0 0 30px rgba(0, 245, 255, 0.3),
            inset 0 0 30px rgba(255, 255, 255, 0.05);
    }
    to {
        text-shadow: 0 0 30px rgba(100, 255, 218, 1);
        box-shadow:
            0 0 50px rgba(0, 245, 255, 0.5),
            inset 0 0 30px rgba(255, 255, 255, 0.1);
    }
}

/* Choice Buttons */
.choices-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.choice-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 2rem 1.5rem;
    border: 4px solid #00ffff;
    border-radius: 0;
    background:
        linear-gradient(135deg, #1a0033 0%, #330066 100%),
        repeating-linear-gradient(
            45deg,
            transparent 0px,
            transparent 4px,
            rgba(0, 255, 255, 0.1) 4px,
            rgba(0, 255, 255, 0.1) 8px
        );
    cursor: pointer;
    transition: all 0.1s ease;
    font-family: 'Press Start 2P', monospace;
    font-size: 0.7rem;
    color: #00ffff;
    position: relative;
    overflow: hidden;
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 6px #00ffff,
        6px 6px 0px #000000,
        0 0 20px #00ffff;
    text-shadow:
        2px 2px 0px #000000,
        0 0 10px #00ffff;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.choice-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.choice-btn:hover::before {
    transform: translateX(100%);
}

.choice-btn:hover {
    transform: translate(-3px, -3px);
    border-color: #ffff00;
    color: #ffff00;
    text-shadow:
        2px 2px 0px #000000,
        0 0 15px #ffff00;
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 6px #ffff00,
        9px 9px 0px #000000,
        0 0 30px #ffff00;
}

.choice-btn.selected {
    border-color: #ff0080;
    color: #ff0080;
    text-shadow:
        2px 2px 0px #000000,
        0 0 20px #ff0080;
    transform: translate(-2px, -2px);
    box-shadow:
        inset 0 0 0 2px #000000,
        inset 0 0 0 6px #ff0080,
        8px 8px 0px #000000,
        0 0 40px #ff0080;
    animation: retro90sPulse 1s ease-in-out infinite alternate;
}

@keyframes retro90sPulse {
    0% {
        box-shadow:
            inset 0 0 0 2px #000000,
            inset 0 0 0 6px #ff0080,
            8px 8px 0px #000000,
            0 0 40px #ff0080;
    }
    100% {
        box-shadow:
            inset 0 0 0 2px #000000,
            inset 0 0 0 6px #ff0080,
            8px 8px 0px #000000,
            0 0 60px #ff0080;
    }
}

.choice-emoji {
    font-size: 3.5rem;
    filter:
        drop-shadow(3px 3px 0px #000000)
        drop-shadow(0 0 10px currentColor);
    transition: all 0.1s ease;
    image-rendering: pixelated;
}

.choice-btn:hover .choice-emoji {
    font-size: 4rem;
    filter:
        drop-shadow(4px 4px 0px #000000)
        drop-shadow(0 0 20px currentColor);
    animation: retro90sFloat 0.3s ease-in-out;
}

.choice-btn.selected .choice-emoji {
    font-size: 3.8rem;
    filter:
        drop-shadow(3px 3px 0px #000000)
        drop-shadow(0 0 15px currentColor);
    animation: retro90sBounce 0.8s ease-in-out infinite alternate;
}

@keyframes retro90sFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-5px); }
}

@keyframes retro90sBounce {
    0% { transform: scale(1); }
    100% { transform: scale(1.05); }
}

.choice-name {
    font-size: 0.6rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    text-shadow: 2px 2px 0px #000000;
    margin-top: 0.5rem;
}

/* Results Screen */
.results-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    gap: 2rem;
    text-align: center;
}

.result-header h2 {
    margin-bottom: 2rem;
    font-size: 2rem;
    color: #333;
}

.choices-display {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin: 2rem 0;
    flex-wrap: wrap;
    gap: 1rem;
}

.player-choice {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.player-name {
    font-weight: 600;
    color: #555;
}

.choice-display {
    font-size: 4rem;
    padding: 1rem;
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: 50%;
    width: 100px;
    height: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vs-divider {
    font-size: 1.5rem;
    font-weight: bold;
    color: #667eea;
}

.result-message {
    font-size: 1.3rem;
    font-weight: 600;
    margin: 2rem 0;
    padding: 1rem;
    border-radius: 10px;
}

.result-message.win {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #2d5a27;
}

.result-message.lose {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #d63031;
}

.result-message.tie {
    background: linear-gradient(135deg, #fd79a8 0%, #fdcb6e 100%);
    color: #2d3436;
}

/* Connection Status */
.connection-status {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255,255,255,0.9);
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    z-index: 1000;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 1001;
}

.toast {
    background: #333;
    color: white;
    padding: 1rem 1.5rem;
    border-radius: 10px;
    margin-bottom: 0.5rem;
    animation: toastSlide 0.3s ease-out;
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

@keyframes toastSlide {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Error Messages */
.error-message {
    color: #e74c3c;
    font-weight: 500;
    text-align: center;
    margin-top: 1rem;
    padding: 0.5rem;
    background: rgba(231, 76, 60, 0.1);
    border-radius: 5px;
    display: none;
}

.error-message.show {
    display: block;
}

/* Status Messages */
.status-message {
    text-align: center;
    font-size: 1.1rem;
    color: #666;
    margin: 2rem 0;
    padding: 1rem;
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    border-radius: 10px;
}

.choice-feedback {
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
    margin-top: 1rem;
    padding: 1rem;
    border-radius: 10px;
    display: none;
}

.choice-feedback.show {
    display: block;
}

.choice-feedback.waiting {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    color: #d63031;
}

.choice-feedback.recorded {
    background: linear-gradient(135deg, #84fab0 0%, #8fd3f4 100%);
    color: #2d5a27;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .screen {
        padding: 1.5rem;
    }
    
    .choices-container {
        grid-template-columns: 1fr;
    }
    
    .choice-btn {
        padding: 1.5rem 1rem;
    }
    
    .choice-emoji {
        font-size: 2.5rem;
    }
    
    .choices-display {
        flex-direction: column;
        gap: 2rem;
    }
    
    .vs-divider {
        order: 2;
    }
    
    .game-header {
        flex-direction: column;
        text-align: center;
    }
}

/* Game Header Styling */
.vs-text {
    color: #00ffff;
    font-weight: bold;
    margin: 0 1rem;
}

@media (max-width: 480px) {
    .room-code {
        font-size: 1.5rem;
    }

    .choice-display {
        width: 80px;
        height: 80px;
        font-size: 3rem;
    }

    .button-group {
        gap: 0.5rem;
    }
}

/* Additional Utility Classes */
.hidden {
    display: none !important;
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Champion Screen Styles */
.result-message.champion {
    background: linear-gradient(135deg, #ffff00, #ff8000, #ff0080);
    border: 4px solid #ffff00;
    animation: championGlow 1.5s ease-in-out infinite alternate;
}

@keyframes championGlow {
    0% {
        box-shadow:
            0 0 20px #ffff00,
            0 0 40px #ff8000,
            0 0 60px #ff0080;
        transform: scale(1);
    }
    100% {
        box-shadow:
            0 0 30px #ffff00,
            0 0 60px #ff8000,
            0 0 90px #ff0080;
        transform: scale(1.02);
    }
}

.champion-message {
    text-align: center;
    color: #000000;
    text-shadow: 2px 2px 0px #ffffff;
}

.champion-message .trophy {
    font-size: 4rem;
    animation: trophyBounce 1s ease-in-out infinite;
    margin-bottom: 1rem;
}

@keyframes trophyBounce {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.champion-message .victory-text {
    font-size: 2rem;
    font-weight: bold;
    margin: 1rem 0;
    color: #000000;
    text-shadow: 3px 3px 0px #ffffff;
    animation: victoryPulse 1.2s ease-in-out infinite;
}

@keyframes victoryPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

.champion-message .final-score {
    font-size: 1.2rem;
    margin: 1rem 0;
    color: #000000;
    font-weight: bold;
}

.champion-message .victory-details {
    font-size: 0.8rem;
    margin: 1rem 0;
    color: #000000;
}

.champion-message .celebration {
    font-size: 1.2rem;
    margin-top: 1rem;
    color: #000000;
    font-weight: bold;
}

.player-scores {
    margin: 1.5rem 0;
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
    align-items: center;
}

.winner-score {
    font-size: 1.4rem;
    font-weight: bold;
    color: #000000;
    text-shadow: 2px 2px 0px #ffffff;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    border: 2px solid #000000;
}

.loser-score {
    font-size: 1.2rem;
    color: #000000;
    text-shadow: 1px 1px 0px #ffffff;
    padding: 0.3rem 0.8rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 6px;
    border: 1px solid #000000;
}

.defeat-message, .tie-message {
    text-align: center;
}

.defeat-message .defeat-text {
    font-size: 1.5rem;
    color: #ff4000;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 0px #000000;
}

.tie-message .tie-text {
    font-size: 1.5rem;
    color: #ffff00;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 0px #000000;
}

.final-score {
    font-size: 1.2rem;
    margin: 1rem 0;
    font-weight: bold;
}

.defeat-details, .tie-details, .victory-details {
    font-size: 0.8rem;
    margin: 1rem 0;
}

.encouragement {
    font-size: 0.9rem;
    margin-top: 1rem;
    color: #00ff00;
}
