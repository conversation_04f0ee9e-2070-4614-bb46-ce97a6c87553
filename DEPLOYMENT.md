# 🚀 Deployment Guide: Render.com + Netlify

This guide will help you deploy the Rock Paper Scissors game with the backend on Render.com and frontend on Netlify.

## 📋 Prerequisites

- GitHub account
- Render.com account (free)
- Netlify account (free)

## 🔧 Step 1: Prepare Your Repository

1. **Fork or clone this repository** to your GitHub account
2. **Make sure you have these files**:
   - Backend files: `server.py`, `game_manager.py`, `requirements.txt`, `render.yaml`
   - Frontend files: `frontend/index.html`, `frontend/style.css`, `frontend/script.js`, `frontend/netlify.toml`

## 🖥️ Step 2: Deploy Backend to Render.com

### Option A: Using Render Dashboard
1. **Go to [Render.com](https://render.com)** and sign up/login
2. **Click "New +"** → **"Web Service"**
3. **Connect your GitHub repository**
4. **Configure the service**:
   - **Name**: `rock-paper-scissors-backend` (or any name you prefer)
   - **Environment**: `Python 3`
   - **Build Command**: `pip install -r requirements.txt`
   - **Start Command**: `python server.py`
   - **Instance Type**: `Free`

5. **Add Environment Variables**:
   - Click "Advanced" → "Add Environment Variable"
   - `DEBUG` = `false`
   - `SECRET_KEY` = `your-random-secret-key-here` (generate a random string)

6. **Click "Create Web Service"**
7. **Wait for deployment** (5-10 minutes)
8. **Copy your Render URL**: `https://your-app-name.onrender.com`

### Option B: Using render.yaml (Automatic)
1. **Push the `render.yaml` file** to your repository
2. **Connect your repository** to Render.com
3. **Render will automatically detect** the configuration and deploy

## 🌐 Step 3: Update Frontend Configuration

1. **Open `frontend/script.js`**
2. **Find this line** (around line 25):
   ```javascript
   production: 'https://your-app-name.onrender.com'
   ```
3. **Replace `your-app-name`** with your actual Render app name
4. **Save the file**

## 📱 Step 4: Deploy Frontend to Netlify

### Option A: Drag and Drop
1. **Go to [Netlify.com](https://netlify.com)** and sign up/login
2. **Drag and drop** the entire `frontend` folder to the Netlify dashboard
3. **Your site is live!** Note the URL: `https://random-name.netlify.app`

### Option B: Git Integration
1. **Go to Netlify dashboard** → **"New site from Git"**
2. **Connect your GitHub repository**
3. **Configure build settings**:
   - **Build command**: (leave empty)
   - **Publish directory**: `frontend`
4. **Deploy site**

### Option C: Using Netlify CLI
```bash
npm install -g netlify-cli
cd frontend
netlify deploy --prod
```

## ✅ Step 5: Test Your Deployment

1. **Open your Netlify URL** in a browser
2. **Check the connection status** (should show green 🟢)
3. **Create a test room** and verify it works
4. **Open another browser/tab** and join the room
5. **Play a game** to ensure everything works!

## 🔧 Troubleshooting

### Backend Issues
- **Check Render logs**: Go to your Render dashboard → Your service → "Logs"
- **Common issues**:
  - Build failures: Check `requirements.txt` syntax
  - Start failures: Check `server.py` for errors
  - Port issues: Render automatically sets the PORT environment variable

### Frontend Issues
- **Check browser console**: Press F12 → Console tab
- **Common issues**:
  - Connection errors: Verify the server URL in `script.js`
  - CORS errors: Check if the backend is running
  - WebSocket issues: Ensure both HTTP and HTTPS protocols match

### Connection Issues
- **Mixed content**: If frontend is HTTPS, backend must be HTTPS too
- **Firewall**: Render.com handles this automatically
- **Cold starts**: Render free tier has cold starts (first request may be slow)

## 🎯 Custom Domain (Optional)

### For Netlify (Frontend)
1. **Go to Netlify dashboard** → Your site → "Domain settings"
2. **Add custom domain** and follow DNS instructions

### For Render (Backend)
1. **Upgrade to paid plan** for custom domain support
2. **Add custom domain** in Render dashboard

## 📊 Monitoring

### Render.com
- **View logs**: Dashboard → Your service → "Logs"
- **Monitor metrics**: Dashboard → Your service → "Metrics"
- **Set up alerts**: Dashboard → Your service → "Settings"

### Netlify
- **View analytics**: Dashboard → Your site → "Analytics"
- **Monitor functions**: Dashboard → Your site → "Functions"
- **Check forms**: Dashboard → Your site → "Forms"

## 💰 Cost Breakdown

### Free Tier Limits
- **Render.com**: 750 hours/month, sleeps after 15 minutes of inactivity
- **Netlify**: 100GB bandwidth/month, 300 build minutes/month

### Paid Options
- **Render.com**: $7/month for always-on service
- **Netlify**: $19/month for pro features

## 🔄 Updates and Maintenance

### Updating the Backend
1. **Push changes** to your GitHub repository
2. **Render automatically redeploys** (if auto-deploy is enabled)

### Updating the Frontend
1. **Update files** in the `frontend` folder
2. **Redeploy to Netlify**:
   - Drag and drop: Upload new files
   - Git integration: Push to repository
   - CLI: Run `netlify deploy --prod`

## 🎉 You're Done!

Your Rock Paper Scissors game is now live and ready to share with friends!

- **Frontend URL**: `https://your-site.netlify.app`
- **Backend URL**: `https://your-app.onrender.com`

Share the frontend URL with friends and start playing! 🎮

## 📞 Support

If you encounter issues:
1. Check the troubleshooting section above
2. Review Render and Netlify documentation
3. Check browser console for errors
4. Verify all URLs are correct in the configuration
