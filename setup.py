#!/usr/bin/env python3
"""
Setup script for Rock Paper Scissors Online Game
This script helps configure the frontend with your Render.com backend URL
"""

import os
import sys

def update_config_file(render_url):
    """Update the frontend config.js file with the Render URL"""
    config_path = os.path.join('frontend', 'config.js')
    
    if not os.path.exists(config_path):
        print(f"❌ Config file not found: {config_path}")
        return False
    
    try:
        with open(config_path, 'r') as f:
            content = f.read()
        
        # Replace the placeholder URL
        updated_content = content.replace(
            'https://your-app-name.onrender.com',
            render_url
        )
        
        with open(config_path, 'w') as f:
            f.write(updated_content)
        
        print(f"✅ Updated config.js with server URL: {render_url}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating config file: {e}")
        return False

def update_script_fallback(render_url):
    """Update the fallback URL in script.js"""
    script_path = os.path.join('frontend', 'script.js')
    
    if not os.path.exists(script_path):
        print(f"❌ Script file not found: {script_path}")
        return False
    
    try:
        with open(script_path, 'r') as f:
            content = f.read()
        
        # Replace the placeholder URL in the fallback
        updated_content = content.replace(
            ': \'https://your-app-name.onrender.com\'; // Replace with your actual URL',
            f': \'{render_url}\';'
        )
        
        with open(script_path, 'w') as f:
            f.write(updated_content)
        
        print(f"✅ Updated script.js fallback URL: {render_url}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating script file: {e}")
        return False

def main():
    print("🎮 Rock Paper Scissors Online - Setup Script")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        render_url = sys.argv[1]
    else:
        print("Please enter your Render.com backend URL:")
        print("Example: https://rock-paper-scissors-backend.onrender.com")
        render_url = input("URL: ").strip()
    
    if not render_url:
        print("❌ No URL provided. Exiting.")
        return
    
    # Validate URL format
    if not render_url.startswith('https://'):
        print("❌ URL must start with 'https://'")
        return
    
    if not render_url.endswith('.onrender.com'):
        print("⚠️  Warning: URL doesn't look like a Render.com URL")
        confirm = input("Continue anyway? (y/N): ").strip().lower()
        if confirm != 'y':
            return
    
    print(f"\n🔧 Configuring frontend for backend: {render_url}")
    
    # Update both files
    config_success = update_config_file(render_url)
    script_success = update_script_fallback(render_url)
    
    if config_success and script_success:
        print("\n🎉 Setup completed successfully!")
        print("\nNext steps:")
        print("1. Deploy the backend to Render.com")
        print("2. Deploy the frontend folder to Netlify")
        print("3. Share your Netlify URL with friends!")
        print("\nFor detailed instructions, see DEPLOYMENT.md")
    else:
        print("\n❌ Setup failed. Please check the error messages above.")

if __name__ == "__main__":
    main()
