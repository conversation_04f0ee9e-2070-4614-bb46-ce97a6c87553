// Configuration file for easy server URL management
// Update this file with your actual Render.com URL after deployment

const CONFIG = {
    // Local development server
    LOCAL_SERVER: 'http://localhost:5000',
    
    // Production server (Replace with your actual Render.com URL)
    PRODUCTION_SERVER: 'https://your-app-name.onrender.com',
    
    // Automatically detect environment
    getServerUrl: function() {
        const isLocal = window.location.hostname === 'localhost' || 
                       window.location.hostname === '127.0.0.1' ||
                       window.location.hostname === '';
        
        return isLocal ? this.LOCAL_SERVER : this.PRODUCTION_SERVER;
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CONFIG;
}
